import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { visualizer } from 'rollup-plugin-visualizer'
import federation from '@originjs/vite-plugin-federation'

// https://vite.dev/config/
export default defineConfig({
  // 为 Node.js 模块提供浏览器兼容的空实现
  define: {
    global: 'globalThis',
    'process.env.NODE_ENV': '"development"',
    // 内网部署：确保不会尝试访问外部资源
    __INTRANET_MODE__: true,
    __DISABLE_EXTERNAL_RESOURCES__: true,
    __OFFLINE_MODE__: true,
    // 禁用segment analytics在浏览器环境中的使用
    'process.env.SEGMENT_ANALYTICS_DISABLED': '"true"',
    // React 兼容性定义
    '__DEV__': 'false',
    '__REACT_DEVTOOLS_GLOBAL_HOOK__': '{}'
  },
  
  // 解析配置 - 排除Node.js专用包
  resolve: {
    alias: {
      // 为Node.js专用包提供空实现
      '@segment/analytics-node': 'virtual:segment-mock',
      'node-fetch': false,
    }
  },

  plugins: [
    react(),
    // Temporarily disable Module Federation due to initialization issues
    // Will implement custom remote loading solution
    // federation({
    //   name: 'ynnx-ai-platform',
    //   remotes: {
    //     openVSCode: process.env.VITE_OPENVSCODE_URL || 'http://192.168.1.3:3667/remoteEntry.js'
    //   },
    //   shared: {
    //     'react': {
    //       singleton: true,
    //       requiredVersion: '^18.3.1',
    //       eager: false
    //     },
    //     'react-dom': {
    //       singleton: true,
    //       requiredVersion: '^18.3.1',
    //       eager: false
    //     }
    //   }
    // }),
    visualizer({ open: false, filename: 'logs/stats.html' }),
    // 替换segment imports插件
    {
      name: 'replace-segment-imports',
             generateBundle(options, bundle) {
         for (const [, chunk] of Object.entries(bundle)) {
           if (chunk.type === 'chunk' && chunk.code) {
             // 替换所有segment imports
             chunk.code = chunk.code
               .replace(/import\s*"@segment\/analytics-node"\s*;?/g, '/* @segment/analytics-node removed */')
               .replace(/import\s*['"]@segment\/analytics-node['"]\s*;?/g, '/* @segment/analytics-node removed */')
               .replace(/from\s*['"]@segment\/analytics-node['"]/g, 'from "data:text/javascript,export default {};"');
           }
         }
      }
    }
  ],
  server: {
    host: '0.0.0.0',
    port: 5173,
    strictPort: false,
    open: false
  },
  build: {
    // 优化构建性能和兼容性
    target: 'es2022', // Support top-level await for Module Federation
    cssTarget: 'chrome61',
    minify: false, // Disable minification to prevent variable hoisting issues
    cssMinify: 'esbuild', // CSS 使用 esbuild 压缩
    modulePreload: {
      polyfill: true
    },
    sourcemap: false, // 生产环境关闭 sourcemap
    reportCompressedSize: process.env.BUILD_VERBOSE === 'true', // 可选择性显示压缩大小详情
    
    rollupOptions: {
      // 外部化 Node.js 模块，避免浏览器兼容性警告  
      external: ['@segment/analytics-node', 'node-fetch'],

      output: {
        // Simplified chunking strategy to avoid variable initialization issues
        manualChunks: (id) => {
          if (id.includes('node_modules')) {
            // React core - keep these together to avoid dependency issues
            if (id.includes('react-dom') || id.includes('react/')) return 'vendor-react';
            
            // Icons
            if (id.includes('react-icons') || id.includes('lucide-react')) return 'vendor-icons';

            // Syntax highlighting
            if (id.includes('highlight.js') || id.includes('prism') || id.includes('react-markdown') ||
                id.includes('rehype') || id.includes('remark')) return 'vendor-syntax';

            // Everything else
            return 'vendor';
          }

          // Simplified component chunking
          if (id.includes('src/components')) {
            if (id.includes('DocumentationSection')) return 'component-docs';
            if (id.includes('DownloadsSection')) return 'component-downloads';
            if (id.includes('WebIDESection')) return 'component-webide';
            return 'components';
          }
        },
        
        // 文件命名优化
        chunkFileNames: () => {
          return `assets/[name]-[hash].js`;
        },
        
        assetFileNames: (assetInfo) => {
          if (/\.(png|jpe?g|svg|gif|tiff|bmp|ico)$/i.test(assetInfo.name)) {
            return `assets/images/[name]-[hash][extname]`;
          }
          if (/\.(css)$/i.test(assetInfo.name)) {
            return `assets/css/[name]-[hash][extname]`;
          }
          return `assets/[name]-[hash][extname]`;
        }
      }
    },
    
    // 提升chunk大小警告阈值，适应现代应用需求
    chunkSizeWarningLimit: 1500, // 提高到 1.5MB，现代应用的合理阈值
    
    // 资源内联阈值
    assetsInlineLimit: 4096
  },
  
  // 优化依赖预构建
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-icons/fa',
      'react-icons/hi',
      'lucide-react',
      'axios'
    ],
    exclude: [
      'openai', // 后端专用
      '@anthropic-ai/sdk', // 后端专用
      'express', // 后端专用
      'cors', // 后端专用
      'ldapjs', // 后端专用
      'node-fetch', // 后端专用
      'dotenv', // 后端专用
      'uuid', // 按需导入
      'concurrently', // 开发工具
      'react-router-dom', // 当前未使用
      '@segment/analytics-node', // Node.js 专用，避免浏览器构建警告
      '@segment/analytics-next'
    ]
  },
  
  // CSS优化
  css: {
    devSourcemap: false,
    preprocessorOptions: {
      scss: {
        charset: false
      }
    }
  },

  // 静态资源处理 - 支持现代图片格式
  assetsInclude: ['**/*.webp', '**/*.avif', '**/*.jpg', '**/*.jpeg', '**/*.png'],
  
  // 性能优化
  esbuild: {
    drop: ['console', 'debugger'],
    legalComments: 'none'
  }
})
