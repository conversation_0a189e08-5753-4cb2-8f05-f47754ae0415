import React, { useState, useEffect } from 'react';
import { FaCog, FaCheckCircle, FaExclamationCircle, FaEye, FaEyeSlash } from 'react-icons/fa';
import apiKeyService from '../services/apiKeyService';
import { getLiteLLMApiBase } from '../config/apiConfig.js';

const LiteLLMConfig = ({ user }) => {
  const [masterKey, setMasterKey] = useState('');
  const [showMasterKey, setShowMasterKey] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('unknown');
  const [isValidKey, setIsValidKey] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  useEffect(() => {
    // 加载已保存的主密钥
    const savedKey = localStorage.getItem('litellm_master_key');
    if (savedKey) {
      setMasterKey(savedKey);
    }
    checkConnection();
  }, []);

  const checkConnection = async () => {
    try {
      const isConnected = await apiKeyService.testConnection();
      setConnectionStatus(isConnected ? 'connected' : 'disconnected');
      
      if (isConnected) {
        const isValid = await apiKeyService.validateMasterKey();
        setIsValidKey(isValid);
      }
    } catch (error) {
      setConnectionStatus('error');
      console.error('连接检查失败:', error);
    }
  };

  const handleSaveMasterKey = async () => {
    if (!masterKey.trim()) {
      setError('请输入有效的主密钥');
      return;
    }

    setIsTesting(true);
    setError(null);
    setSuccess(null);

    try {
      // 设置新的主密钥
      apiKeyService.setMasterKey(masterKey);
      
      // 测试新密钥
      const isValid = await apiKeyService.validateMasterKey();
      
      if (isValid) {
        setIsValidKey(true);
        setSuccess('主密钥设置成功！');
        await checkConnection();
      } else {
        setError('主密钥无效，请检查密钥是否正确');
        setIsValidKey(false);
      }
    } catch (error) {
      setError(`设置主密钥失败: ${error.message}`);
    } finally {
      setIsTesting(false);
    }
  };

  const handleTestConnection = async () => {
    setIsTesting(true);
    setError(null);
    setSuccess(null);

    try {
      const isConnected = await apiKeyService.testConnection();
      
      if (isConnected) {
        const isValid = await apiKeyService.validateMasterKey();
        setConnectionStatus('connected');
        setIsValidKey(isValid);
        
        if (isValid) {
          setSuccess('连接成功，主密钥有效！');
        } else {
          setError('连接成功，但主密钥无效');
        }
      } else {
        setConnectionStatus('disconnected');
        setError('无法连接到LiteLLM服务');
      }
    } catch (error) {
      setError(`连接测试失败: ${error.message}`);
      setConnectionStatus('error');
    } finally {
      setIsTesting(false);
    }
  };

  const renderStatusIcon = () => {
    if (connectionStatus === 'connected' && isValidKey) {
      return <FaCheckCircle className="text-green-400" />;
    } else {
      return <FaExclamationCircle className="text-red-400" />;
    }
  };

  const getStatusText = () => {
    if (connectionStatus === 'connected' && isValidKey) {
      return '连接正常，主密钥有效';
    } else if (connectionStatus === 'connected' && !isValidKey) {
      return '连接正常，主密钥无效';
    } else if (connectionStatus === 'disconnected') {
      return '无法连接到LiteLLM服务';
    } else if (connectionStatus === 'error') {
      return '连接错误';
    } else {
      return '检查中...';
    }
  };

  // 只有管理员才能看到此配置
  if (!user || (user.username !== 'kfb-duys' && user.username !== 'admin' && !user.name.includes('管理'))) {
    return null;
  }

  return (
    <div
      className="bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-2xl p-8"
    >
      <div className="flex items-center gap-3 mb-6">
        <FaCog className="text-cyan-400 text-xl" />
        <h3 className="text-lg font-semibold text-white">LiteLLM 配置管理</h3>
      </div>

      {/* 状态显示 */}
      <div className="mb-6 p-4 bg-gray-800 rounded-lg">
        <div className="flex items-center gap-3 mb-2">
          {renderStatusIcon()}
          <span className="text-white font-medium">服务状态</span>
        </div>
        <p className="text-sm text-gray-400 mb-2">{getStatusText()}</p>
                        <p className="text-xs text-gray-500">服务地址: {getLiteLLMApiBase()}</p>
      </div>

      {/* 主密钥配置 */}
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            LiteLLM 主密钥
          </label>
          <div className="relative">
            <input
              type={showMasterKey ? 'text' : 'password'}
              value={masterKey}
              onChange={(e) => setMasterKey(e.target.value)}
              placeholder="sk-..."
              className="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 pr-10 text-white placeholder-gray-500 focus:border-cyan-500 focus:outline-none"
            />
            <button
              type="button"
              onClick={() => setShowMasterKey(!showMasterKey)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
            >
              {showMasterKey ? <FaEyeSlash /> : <FaEye />}
            </button>
          </div>
          <p className="text-xs text-gray-500 mt-1">
            主密钥用于管理LiteLLM代理中的虚拟密钥
          </p>
        </div>

        {/* 错误和成功消息 */}
        {error && (
          <div className="p-3 bg-red-900/50 border border-red-700 rounded-lg">
            <p className="text-red-300 text-sm">{error}</p>
          </div>
        )}

        {success && (
          <div className="p-3 bg-green-900/50 border border-green-700 rounded-lg">
            <p className="text-green-300 text-sm">{success}</p>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex gap-3">
          <button
            onClick={handleSaveMasterKey}
            disabled={isTesting}
            className="flex-1 bg-cyan-500 hover:bg-cyan-600 disabled:bg-cyan-700 text-white py-2 px-4 rounded-lg transition-colors disabled:cursor-not-allowed"
          >
            {isTesting ? '保存中...' : '保存主密钥'}
          </button>
          
          <button
            onClick={handleTestConnection}
            disabled={isTesting}
            className="flex-1 bg-gray-700 hover:bg-gray-600 disabled:bg-gray-800 text-white py-2 px-4 rounded-lg transition-colors disabled:cursor-not-allowed"
          >
            {isTesting ? '测试中...' : '测试连接'}
          </button>
        </div>
      </div>

      {/* 配置说明 */}
      <div className="mt-6 p-4 bg-blue-900/20 border border-blue-700/50 rounded-lg">
        <h4 className="text-sm font-medium text-blue-300 mb-2">配置说明</h4>
        <ul className="text-xs text-gray-400 space-y-1">
          <li>• 主密钥用于管理LiteLLM代理中的所有虚拟密钥</li>
                          <li>• 确保LiteLLM服务正在{getLiteLLMApiBase()}上运行</li>
          <li>• 主密钥必须以"sk-"开头</li>
          <li>• 配置成功后，用户将能够管理自己的API密钥</li>
        </ul>
      </div>
    </div>
  );
};

export default LiteLLMConfig; 