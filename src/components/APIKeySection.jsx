import React, { useState, useEffect, useCallback } from 'react';
import { FaKey, FaCopy, FaEye, FaEyeSlash, FaCheckCircle, FaTrash, FaPlus } from 'react-icons/fa';
import { getLiteLLMApiBase } from '../config/apiConfig.js';
import apiKeyService from '../services/apiKeyService';

const APIKeySection = ({ user, onLogin }) => {
  const [userInfo, setUserInfo] = useState(null);
  const [userKeys, setUserKeys] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isCreatingUser, setIsCreatingUser] = useState(false);
  const [isGeneratingKey, setIsGeneratingKey] = useState(false);
  const [error, setError] = useState(null);
  const [copied, setCopied] = useState(false);
  const [showKey, setShowKey] = useState({});
  const [connectionStatus, setConnectionStatus] = useState('connected');
  const [loadingStep, setLoadingStep] = useState('');

  // 缓存连接状态和主密钥验证结果
  const [connectionCache, setConnectionCache] = useState({
    lastCheck: 0,
    isConnected: null,
    isValidKey: null
  });

  // 检查连接状态（带缓存，5分钟有效期）
  const checkConnectionWithCache = useCallback(async () => {
    const now = Date.now();
    const cacheValidTime = 5 * 60 * 1000; // 5分钟缓存
    
    // 如果缓存仍然有效，直接使用缓存结果
    if (connectionCache.lastCheck && (now - connectionCache.lastCheck) < cacheValidTime) {
      setConnectionStatus(connectionCache.isConnected ? 'connected' : 'disconnected');
      return connectionCache.isConnected && connectionCache.isValidKey;
    }

    try {
      // 并行检查连接和验证主密钥
      const [isConnected, isValidKey] = await Promise.all([
        apiKeyService.testConnection(),
        apiKeyService.validateMasterKey()
      ]);
      
      // 更新缓存
      setConnectionCache({
        lastCheck: now,
        isConnected,
        isValidKey
      });
      
      setConnectionStatus(isConnected ? 'connected' : 'disconnected');
      
      if (isConnected && !isValidKey) {
        setError('主密钥无效，请联系管理员检查LiteLLM配置');
        return false;
      }
      
      return isConnected && isValidKey;
    } catch (error) {
      console.error('连接检查失败:', error);
      setConnectionStatus('disconnected');
      return false;
    }
  }, [connectionCache]);

  // 优化的用户数据加载函数
  const loadUserData = useCallback(async () => {
    if (!user) return;
    
    setIsLoading(true);
    setError(null);
    setLoadingStep('正在加载用户数据...');
    
    try {
      const userId = user.id || user.username || user.email;
      
      // 并行获取用户信息和密钥，提升加载速度
      const [userInfoResult, userKeysResult] = await Promise.allSettled([
        apiKeyService.getUserInfo(userId),
        apiKeyService.getUserKeys(userId) // 直接获取密钥，不依赖用户信息
      ]);
      
      // 处理用户信息
      if (userInfoResult.status === 'fulfilled') {
        setUserInfo(userInfoResult.value);
      } else {
        // 如果用户不存在，尝试创建用户
        console.log('用户不存在，尝试创建新用户...');
        setIsCreatingUser(true);
        
        const newUser = await apiKeyService.createUser(
          user.email || `${user.username}@example.com`,
          user.name || user.username
        );
        
        setUserInfo(newUser);
        setIsCreatingUser(false);
      }
      
      // 处理用户密钥
      if (userKeysResult.status === 'fulfilled') {
        setUserKeys(userKeysResult.value);
      } else {
        console.error('获取用户密钥失败:', userKeysResult.reason);
        setUserKeys([]);
      }
      
    } catch (error) {
      console.error('加载用户数据失败:', error);
      setError(`加载用户数据失败: ${error.message}`);
    } finally {
      setIsLoading(false);
      setLoadingStep('');
    }
  }, [user]);

  // 初始化时只检查连接状态，不阻塞用户数据加载
  useEffect(() => {
    checkConnectionWithCache();
  }, [checkConnectionWithCache]);

  // 当用户登录时立即开始加载数据，不等待连接检查完成
  useEffect(() => {
    if (user) {
      // 立即开始加载用户数据，同时在后台检查连接
      loadUserData();
      
      // 如果连接状态未知，在后台检查
      if (connectionStatus === 'checking') {
        checkConnectionWithCache();
      }
    }
  }, [user, loadUserData, checkConnectionWithCache, connectionStatus]);

  const handleCopy = (key) => {
    if (key) {
      navigator.clipboard.writeText(key);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const handleGenerateNewKey = async () => {
    if (!userInfo) return;
    
    setIsGeneratingKey(true);
    setError(null);
    
    try {
      await apiKeyService.generateKey({
        user_id: userInfo.user_id,
        models: ['deepseek-chat', 'qwen3-235b-a22b', 'qwen3-235b-a22b'],
        key_alias: userInfo.user_id, // 设置Key Alias为用户ID
        metadata: {
          user_name: user.name,
          created_via: 'ynnx-ai-platform'
        }
      });
      
      // 密钥生成成功
      
      // 等待一小段时间确保localStorage保存完成
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // 重新加载用户数据以获取最新密钥列表
      await loadUserData();
    } catch (error) {
      console.error('生成密钥失败:', error);
      setError(`生成密钥失败: ${error.message}`);
    } finally {
      setIsGeneratingKey(false);
    }
  };

  const handleDeleteKey = async (keyData) => {
    if (!confirm('确定要删除这个API密钥吗？删除后您可以重新生成一个新的密钥。此操作不可撤销。')) {
      return;
    }
    
    // 添加删除状态指示
    setIsLoading(true);
    setLoadingStep('正在删除密钥...');
    setError(null);
    
    try {
      // 检查删除标识符
      const keyToDelete = keyData.token;
      if (!keyToDelete) {
        throw new Error('删除失败：密钥缺少必要的删除标识符。请刷新页面重试，如果问题持续存在，请联系管理员。');
      }

      console.log(`正在删除密钥，token: ${keyToDelete.substring(0, 10)}...`);
      await apiKeyService.deleteKey(keyToDelete);
      console.log('密钥删除成功');
      
      // 重新加载用户数据
      setLoadingStep('正在刷新数据...');
      await loadUserData();
      
      // 显示成功消息
      setLoadingStep('删除成功');
      setTimeout(() => {
        setLoadingStep('');
      }, 2000);
      
    } catch (error) {
      console.error('删除密钥失败:', error);
      
      // 错误信息处理
      let errorMessage = `删除密钥失败: ${error.message}`;
      
      if (error.message.includes('token标识符') || error.message.includes('删除标识符')) {
        errorMessage = error.message;
      } else if (error.message.includes('401') || error.message.includes('unauthorized')) {
        errorMessage = '认证失败：请刷新页面重试，或联系管理员检查系统配置';
      } else if (error.message.includes('404')) {
        errorMessage = '密钥不存在：该密钥可能已被删除，请刷新页面查看最新状态';
      } else if (error.message.includes('network') || error.message.includes('fetch')) {
        errorMessage = '网络错误：无法连接到服务器，请检查网络连接后重试';
      }
      
      setError(errorMessage);
    } finally {
      setIsLoading(false);
      setLoadingStep('');
    }
  };

  const toggleShowKey = (keyIndex) => {
    setShowKey(prev => ({
      ...prev,
      [keyIndex]: !prev[keyIndex]
    }));
  };

  // 渲染连接状态指示器
  const renderConnectionStatus = () => {
    // 获取主密钥状态
    const masterKeyStatus = apiKeyService.getMasterKeyStatus();
    
    return (
      <div className="space-y-2 mb-4">
        {/* LiteLLM连接状态 */}
        <div className="flex items-center gap-2">
          <div className={`w-3 h-3 rounded-full ${
            connectionStatus === 'connected' ? 'bg-green-400' : 
            connectionStatus === 'disconnected' ? 'bg-red-400' : 'bg-yellow-400'
          }`}></div>
          <span className="text-sm text-gray-400">
            LLM连接状态: {
              connectionStatus === 'connected' ? '已连接' : 
              connectionStatus === 'disconnected' ? '未连接' : '检查中...'
            }
            &nbsp;&nbsp;LLM API: {getLiteLLMApiBase()}
          </span>
        </div>
        
        {/* 主密钥状态提示 */}
        {masterKeyStatus.usingDefault ? (
          <div className="flex items-center gap-2 p-3 bg-yellow-900/30 border border-yellow-600/30 rounded-lg">
            <div className="w-3 h-3 rounded-full bg-yellow-400"></div>
            <div className="flex-1">
              <span className="text-sm text-yellow-300 font-medium">主密钥状态: 使用默认配置</span>
              <p className="text-xs text-yellow-200 mt-1">
                检测到您可能清除了浏览器缓存，正在使用默认主密钥。如果删除操作失败，请联系管理员重新配置主密钥。
              </p>
            </div>
          </div>
        ) : null}
        
        {masterKeyStatus.hasStoredKey ? (
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-green-400"></div>
            <span className="text-sm text-gray-400">主密钥状态: 已配置</span>
          </div>
        ) : null}
      </div>
    );
  };

  return (
    <section id="api-key" className="py-20 bg-black relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-t from-gray-900 to-black"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 标题 */}
        <div
          className="text-center mb-12"
        >
          <h2 className="text-5xl font-bold mb-4 bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent">
            API 密钥管理
          </h2>
          <p className="text-xl text-gray-400">
            安全地管理您的 大模型LLM API 访问凭证
          </p>
        </div>

        {/* 连接状态 */}
        {renderConnectionStatus()}

        {user ? (
          <>
            <div
              className="bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-2xl p-8"
            >
              {/* 错误信息 */}
              {error ? (
                <div className="mb-6 p-4 bg-red-900/50 border border-red-700 rounded-lg">
                  <p className="text-red-300">{error}</p>
                </div>
              ) : null}

              {/* 加载状态 */}
              {(isLoading || isCreatingUser) ? (
                <div className="mb-6 p-4 bg-blue-900/50 border border-blue-700 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-300"></div>
                  <p className="text-blue-300">
                      {isCreatingUser ? '正在创建用户...' : loadingStep || '正在加载...'}
                  </p>
                  </div>
                </div>
              ) : null}

              {/* 用户信息 */}
              {userInfo ? (
                <div className="mb-8 pb-6 border-b border-gray-700">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-semibold text-white">当前用户</h3>
                      <p className="text-gray-400">{user.name}</p>
                      <p className="text-sm text-gray-500">用户ID: {userInfo.user_id}</p>
                    </div>
                    <div className="text-sm text-gray-400">
                      <p>状态: 已连接</p>
                      <p>密钥数量: {userKeys.length || '无'}</p>
                    </div>
                  </div>
                </div>
              ) : null}

              {/* API密钥列表 */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <FaKey className="text-cyan-400 text-xl" />
                    <h3 className="text-lg font-semibold text-white">您的 API 密钥</h3>
                    <span className="text-sm text-gray-500">(每个用户限制1个)</span>
                  </div>
                  
                  {connectionStatus === 'connected' && userKeys.length === 0 ? (
                    <button
                      onClick={handleGenerateNewKey}
                      disabled={isGeneratingKey}
                      className="inline-flex items-center gap-2 px-4 py-2 bg-cyan-500 hover:bg-cyan-600 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <FaPlus />
                      {isGeneratingKey ? '生成中...' : '生成API密钥'}
                    </button>
                  ) : null}
                </div>

                {userKeys.length === 0 ? (
                  <div className="bg-gray-800 rounded-lg p-8 text-center">
                    <FaKey className="text-4xl text-gray-600 mx-auto mb-4" />
                    <p className="text-gray-400 mb-4">您还没有API密钥</p>
                    {connectionStatus === 'connected' ? (
                      <button
                        onClick={handleGenerateNewKey}
                        disabled={isGeneratingKey}
                        className="px-6 py-2 bg-cyan-500 hover:bg-cyan-600 text-white rounded-lg transition-colors disabled:opacity-50"
                      >
                        {isGeneratingKey ? '生成中...' : '生成API密钥'}
                      </button>
                    ) : null}
                  </div>
                ) : (
                  userKeys.map((keyData, index) => (
                    <div key={index} className="bg-gray-800 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex-1 mr-4">
                          <input
                            type={showKey[index] ? 'text' : 'password'}
                            value={keyData.key || keyData.token || ''}
                            readOnly
                            className="w-full bg-transparent text-gray-300 font-mono text-sm outline-none"
                          />
                          {keyData.isPartialKey ? (
                            <p className="text-xs text-yellow-400 mt-1">
                              ⚠️ 显示的是截断密钥，完整密钥仅在生成时显示一次
                            </p>
                          ) : null}
                        </div>
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => toggleShowKey(index)}
                            className="p-2 text-gray-400 hover:text-white transition-colors"
                            title="显示/隐藏密钥"
                          >
                            {showKey[index] ? <FaEyeSlash /> : <FaEye />}
                          </button>
                          <button
                            onClick={() => handleCopy(keyData.key || keyData.token)}
                            className="p-2 text-gray-400 hover:text-white transition-colors"
                            title="复制密钥"
                          >
                            <>
                              {copied ? (
                                <div
                                                                      key="check"
                                >
                                  <FaCheckCircle className="text-green-400" />
                                </div>
                              ) : (
                                <div
                                  key="copy"
                                >
                                  <FaCopy />
                                </div>
                              )}
                            </>
                          </button>
                          <button
                            onClick={() => handleDeleteKey(keyData)}
                             className="p-2 text-gray-400 hover:text-red-400 transition-colors"
                            title="删除密钥"
                           >
                             <FaTrash />
                           </button>
                        </div>
                      </div>
                      
                      {/* 密钥信息 */}
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-xs text-gray-400">
                        <div>
                          <span className="block text-gray-500">模型:</span>
                          <span>{keyData.models ? keyData.models.join(', ') : '所有模型'}</span>
                        </div>
                        <div>
                          <span className="block text-gray-500">创建时间:</span>
                          <span>{keyData.created_at ? new Date(keyData.created_at).toLocaleDateString() : '未知'}</span>
                        </div>
                        <div>
                          <span className="block text-gray-500">状态:</span>
                          <span className={keyData.blocked ? 'text-red-400' : 'text-green-400'}>
                            {keyData.blocked ? '已阻止' : '活跃'}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))
                )}
                
                              {/* 当用户已有密钥时显示的提示信息 */}
              {userKeys.length > 0 ? (
                <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4 mt-4">
                  <div className="flex items-center gap-2 text-blue-400">
                    <FaKey className="text-sm" />
                    <span className="text-sm font-medium">密钥使用说明</span>
                  </div>
                  <p className="text-sm text-gray-300 mt-2">
                    每个用户最多只能拥有1个API密钥。如需更换密钥，请先删除当前密钥，然后重新生成。
                  </p>
                  <p className="text-sm text-gray-300 mt-2">
                    密钥生成后，请妥善保管，不要泄露给他人。
                  </p>
                  <p className="text-sm text-gray-300 mt-2">
                    模型API地址默认为：{getLiteLLMApiBase()}，具体设置方式参考文档
                  </p>
                </div>
              ) : null}
            </div>
            </div>

{/* LiteLLM配置现在从配置文件统一管理，不再需要前端配置界面 */}


          </>
        ) : (
          <div
            className="text-center py-16"
          >
            <FaKey className="text-6xl text-gray-600 mx-auto mb-4" />
            <h3 className="text-2xl font-semibold text-white mb-2">请先登录</h3>
            <p className="text-gray-400 mb-6">登录后即可查看和管理您的 API 密钥</p>
            <button
              onClick={onLogin}
              className="px-6 py-3 bg-gradient-to-r from-cyan-400 to-blue-500 text-black font-semibold rounded-lg"
            >
              立即登录
            </button>
          </div>
        )}
      </div>
    </section>
  );
};

export default APIKeySection; 