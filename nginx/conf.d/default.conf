# 上游服务器配置
upstream ldap_backend {
    server app:3002;
    keepalive 8;
}

# WebSocket连接升级映射
map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
}

# HTTP服务器 - 重定向到HTTPS
server {
    listen 80;
    server_name localhost ***********;
    return 301 https://$host$request_uri;
}

# HTTPS服务器
server {
    listen 443 ssl;
    http2 on;
    server_name localhost ***********;
    root /usr/share/nginx/html;

    # SSL配置
    ssl_certificate /etc/nginx/ssl/nginx.crt;
    ssl_certificate_key /etc/nginx/ssl/nginx.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 增加客户端请求体大小限制
    client_max_body_size 10M;

    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;

    # LiteLLM API代理
    location /api/litellm/ {
        proxy_pass http://***********:4000/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 10s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # CORS头部支持
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS, PUT, DELETE" always;
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,Accept,Origin" always;
        
        # Handle preflight requests
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, POST, OPTIONS, PUT, DELETE";
            add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,Accept,Origin";
            add_header Access-Control-Max-Age 1728000;
            add_header Content-Type "text/plain; charset=utf-8";
            add_header Content-Length 0;
            return 204;
        }
    }

    # LDAP认证API代理
    location /api/ldap/ {
        proxy_pass http://ldap_backend/api/ldap/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 5s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # OpenVSCode代理 - 解决iframe Cookie认证问题
    location /ide/ {
        # 移除前缀，直接代理到OpenVSCode
        rewrite ^/ide/(.*)$ /$1 break;
        proxy_pass http://***********:3667;
        proxy_http_version 1.1;
        
        # 关键：保持Host头和添加认证token
        proxy_set_header Host ***********:3667;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket支持
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        
        # 解决Cookie问题：在代理层自动添加认证Cookie
        proxy_set_header Cookie "vscode-tkn=tk-ynnx-llm; $http_cookie";
        
        # 超时设置
        proxy_connect_timeout 10s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
        
        # 缓冲设置
        proxy_buffering off;
        proxy_request_buffering off;
        
        # 安全头部移除，避免iframe限制
        proxy_hide_header X-Frame-Options;
        add_header X-Frame-Options "ALLOWALL" always;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://ldap_backend/health;
        access_log off;
    }

    # 静态资源缓存
    location ~* \.(js|mjs|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|wasm)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # 主应用路由
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 错误页面
    error_page 404 /index.html;
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}